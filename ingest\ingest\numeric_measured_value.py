#!/usr/bin/env python3
"""
Module for extracting numeric values from measured_value column in endpoints data.
Handles specific extraction logic for different endpoint types.
"""

import re
import pandas as pd
from typing import Optional


def extract_numeric_for_tgi(value) -> Optional[float]:
    """
    Extract numeric value for TUMOR_GROWTH_INHIBITION endpoint.
    
    Rules:
    - Accept pure numbers (integer or float)
    - Accept numbers with % sign (e.g., "85%", "-66.84%")
    - Reject values containing alphabetic characters or complex text
    
    Args:
        value: The measured_value to process
        
    Returns:
        Extracted numeric value as float, or None if extraction fails
    """
    if pd.isna(value) or value == '' or value is None:
        return None
    
    value_str = str(value).strip()
    
    # Pattern 1: Just a number (integer or float, including negative)
    try:
        return float(value_str)
    except ValueError:
        pass
    
    # Pattern 2: Number with % sign (e.g., "85%", "-66.84%", "91.44%")
    # This regex matches optional negative sign, digits, optional decimal point and digits, followed by optional spaces and %
    percent_pattern = r'^(-?\d+\.?\d*)\s*%$'
    match = re.match(percent_pattern, value_str)
    if match:
        return float(match.group(1))
    
    # If value contains alphabetic characters (excluding % sign), reject it
    # This filters out values like "Complete durable response..." or "74% shrinkage"
    if re.search(r'[a-zA-Z]', value_str):
        return None
    
    return None


def extract_numeric_for_hscore(value) -> Optional[float]:
    """
    Extract numeric value for SPECIFIC_ANTIGEN_EXPRESSION_H_SCORE endpoint.
    
    Rules:
    - Only accept pure numbers (integer or float)
    - Reject any values containing non-numeric characters (except decimal point)
    - Reject ranges or complex expressions
    
    Args:
        value: The measured_value to process
        
    Returns:
        Extracted numeric value as float, or None if extraction fails
    """
    if pd.isna(value) or value == '' or value is None:
        return None
    
    value_str = str(value).strip()
    
    # Only accept pure numbers (integer or float)
    # This regex matches optional negative sign, digits, optional decimal point and more digits
    pure_number_pattern = r'^-?\d+\.?\d*$'
    if re.match(pure_number_pattern, value_str):
        try:
            return float(value_str)
        except ValueError:
            return None
    
    # Reject everything else (ranges like "105-200", text, etc.)
    return None


def add_numeric_measured_value_column(df: pd.DataFrame) -> pd.DataFrame:
    """
    Add numeric_measured_value column to endpoints dataframe.
    
    This function applies the appropriate extraction logic based on the endpoint_name:
    - TUMOR_GROWTH_INHIBITION: Uses extract_numeric_for_tgi()
    - SPECIFIC_ANTIGEN_EXPRESSION_H_SCORE: Uses extract_numeric_for_hscore()
    - All other endpoints: Sets numeric_measured_value to None
    
    Args:
        df: DataFrame containing endpoints data with endpoint_name and measured_value columns
        
    Returns:
        DataFrame with added numeric_measured_value column
    """
    # Initialize the new column with None
    df['numeric_measured_value'] = None
    
    # Apply extraction for TUMOR_GROWTH_INHIBITION
    tgi_mask = df['endpoint_name'] == 'TUMOR_GROWTH_INHIBITION'
    df.loc[tgi_mask, 'numeric_measured_value'] = df.loc[tgi_mask, 'measured_value'].apply(extract_numeric_for_tgi)
    
    # Apply extraction for SPECIFIC_ANTIGEN_EXPRESSION_H_SCORE
    hscore_mask = df['endpoint_name'] == 'SPECIFIC_ANTIGEN_EXPRESSION_H_SCORE'
    df.loc[hscore_mask, 'numeric_measured_value'] = df.loc[hscore_mask, 'measured_value'].apply(extract_numeric_for_hscore)
    
    # Log extraction statistics
    tgi_total = tgi_mask.sum()
    tgi_extracted = df.loc[tgi_mask, 'numeric_measured_value'].notna().sum()
    print(f"  TUMOR_GROWTH_INHIBITION: Extracted {tgi_extracted}/{tgi_total} numeric values ({tgi_extracted/tgi_total*100:.1f}%)" if tgi_total > 0 else "  TUMOR_GROWTH_INHIBITION: No records found")
    
    hscore_total = hscore_mask.sum()
    hscore_extracted = df.loc[hscore_mask, 'numeric_measured_value'].notna().sum()
    print(f"  SPECIFIC_ANTIGEN_EXPRESSION_H_SCORE: Extracted {hscore_extracted}/{hscore_total} numeric values ({hscore_extracted/hscore_total*100:.1f}%)" if hscore_total > 0 else "  SPECIFIC_ANTIGEN_EXPRESSION_H_SCORE: No records found")
    
    return df