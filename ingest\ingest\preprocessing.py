#!/usr/bin/env python3
"""
Simplified preprocessing module for combined ADC-Endpoint data ingestion.
Handles preprocessing for the single joined table.
"""

import pandas as pd
import os
import re
from typing import Dict, Any
# from numeric_measured_value import add_numeric_measured_value_column  # Skipped for now

def standardize_empty_values(df: pd.DataFrame) -> pd.DataFrame:
    """
    Replace various null/empty representations with dash '-'.
    
    Replaces the following values with '-':
    - 'NONE'
    - 'Not Specified' (case variations)
    - 'not specified' 
    - 'Not specified'
    - Empty strings, whitespace-only strings
    - NaN/null values
    
    Args:
        df: DataFrame to standardize
        
    Returns:
        DataFrame with standardized empty values
    """
    df = df.copy()
    
    # Define patterns to replace with dash
    empty_patterns = [
        r'^NONE$',
        r'^Not Specified$',
        r'^not specified$', 
        r'^Not specified$',
        r'^\s*$',  # Empty or whitespace-only strings
        r'^<empty>$'  # Literal <empty> text
    ]
    
    # Apply to all string/object columns
    for col in df.columns:
        if df[col].dtype == 'object':
            # Replace NaN with empty string temporarily for pattern matching
            df[col] = df[col].fillna('')
            
            # Apply regex replacements
            for pattern in empty_patterns:
                df[col] = df[col].astype(str).str.replace(pattern, '-', regex=True)
            
            # Convert empty strings to dash
            df[col] = df[col].replace('', '-')
    
    print(f"Standardized empty values across {len(df.columns)} columns")
    return df

def preprocess_combined_adc_endpoints_data(df: pd.DataFrame) -> pd.DataFrame:
    """
    Preprocess combined ADC-Endpoints data before database insertion.
    Standardizes empty values, fixes id column, applies filtering, and enhances data.
    
    Args:
        df: Raw DataFrame from Combined_ADC_Endpoints sheet
        
    Returns:
        Preprocessed DataFrame
    """
    print("Starting preprocessing of combined ADC-Endpoints data...")
    
    # Step 11: Standardize empty values first
    df = standardize_empty_values(df)
    
    # Step 12: Fix id column by removing '_' and everything after it
    if 'id' in df.columns:
        df['id'] = df['id'].astype(str).str.split('_').str[0]
        print(f"Fixed id column for {len(df)} records")
    
    # # Step 13: Filter by expert_opinion=TRUE and drop the column
    # if 'expert_opinion' in df.columns:
    #     before_expert_filter_count = len(df)
    #     df = df[df['expert_opinion'] == True]
    #     after_expert_filter_count = len(df)
    #     print(f"Filtered by expert_opinion=TRUE: {before_expert_filter_count} -> {after_expert_filter_count} records")
        
    #     # Drop the expert_opinion column since it's no longer needed
    #     df = df.drop('expert_opinion', axis=1)
    #     print(f"Dropped expert_opinion column after filtering")
    # else:
    #     print("Warning: expert_opinion column not found in data")
    
    # # Step 14: Filter by measured_value (non-null/non-empty)
    # if 'measured_value' in df.columns:
    #     before_measured_filter_count = len(df)
    #     #df = df[df['measured_value'].notna() & (df['measured_value'] != '') & (df['measured_value'] != ' ') & (df['measured_value'] != '-')]
    #     after_measured_filter_count = len(df)
    #     print(f"Filtered by non-null measured_value: {before_measured_filter_count} -> {after_measured_filter_count} records")
    # else:
    #     print("Warning: measured_value column not found in data")
    
    # Step 15: Filter by predicted_class=TP (exclude FP)
    # if 'predicted_class' in df.columns:
    #     before_predicted_filter_count = len(df)
    #     df = df[(df['predicted_class'] == 'TP')]
    #     after_predicted_filter_count = len(df)
    #     print(f"Filtered by predicted_class=TP: {before_predicted_filter_count} -> {after_predicted_filter_count} records")
        
    #     # Drop the predicted_class column since it's no longer needed
    #     df = df.drop('predicted_class', axis=1)
    #     print(f"Dropped predicted_class column after filtering")
    # else:
    #     print("Warning: predicted_class column not found in data")
    
    # Keep extraction_id as it's needed for identifying processed entries
    if 'extraction_id' in df.columns:
        print("Keeping extraction_id column for processed entries identification")
    
    # Drop reasoning if present (not needed in final database)
    if 'reasoning' in df.columns:
        df = df.drop('reasoning', axis=1)
        print("Dropped reasoning column")
    
    # Drop support_span if present (not needed in final database)
    if 'support_span' in df.columns:
        df = df.drop('support_span', axis=1)
        print("Dropped support_span column")
    
    # Step 16: Add endpoint_category column by joining with mapping file
    try:
        current_dir = os.path.dirname(os.path.abspath(__file__))
        mapping_file_path = os.path.abspath(os.path.join(current_dir, '..', '..', 'data', 'data', 'preclinical_phase_priority_MAPPING.xlsx'))

        # Load the mapping data
        mapping_df = pd.read_excel(mapping_file_path)
        
        # Join data with mapping data
        # endpoint_name (from data) matches attribute_extraction_name (from mapping)
        df = df.merge(
            mapping_df[['attribute_extraction_name', 'attribute_category', 'Priority']], 
            left_on='endpoint_name', 
            right_on='attribute_extraction_name', 
            how='left',
            suffixes=('', '_mapping')
        )
        
        # # Step 17: Filter by Priority (Prioritize/Proxied)
        # before_filter_count = len(df)
        # df = df[df['Priority'].isin(['Prioritize', 'Proxied'])]
        # after_filter_count = len(df)
        # print(f"Filtered by Priority: {before_filter_count} -> {after_filter_count} records")
        
        # Rename attribute_category to endpoint_category
        df['endpoint_category'] = df['attribute_category']
        
        # Drop temporary columns
        df = df.drop(['attribute_extraction_name', 'attribute_category', 'Priority'], axis=1)
        
        print(f"Added endpoint_category column to {len(df)} records")
        
    except Exception as e:
        print(f"Warning: Could not add endpoint_category column: {e}")
        # Add empty endpoint_category column if mapping fails
        df['endpoint_category'] = '-'
    
    # Step 18: Populate Sources column with DOI links from OpenAlex metadata
    try:
        current_dir = os.path.dirname(os.path.abspath(__file__))
        openalex_file_path = os.path.abspath(os.path.join(current_dir, '..', '..', 'data', 'data', 'openalex_publication_metadata.xlsx'))

        # Load the OpenAlex metadata
        openalex_df = pd.read_excel(openalex_file_path, sheet_name='Publication_Metadata')
        df['id'] = df['id'].astype(str).str.upper()


        # Join with OpenAlex data to get DOI links
        df_with_sources = df.merge(
            openalex_df[['OpenAlex Work ID', 'DOI Link']], 
            left_on='id', 
            right_on='OpenAlex Work ID', 
            how='left'
        )
        
        # Update Sources column with DOI Link
        df_with_sources['Sources'] = df_with_sources['DOI Link']
        
        # Drop temporary OpenAlex columns
        df_with_sources = df_with_sources.drop(['OpenAlex Work ID', 'DOI Link'], axis=1, errors='ignore')
        
        # Count successful matches
        sources_filled = df_with_sources['Sources'].notna().sum()
        print(f"Populated Sources column for {sources_filled} out of {len(df)} records")
        
        df = df_with_sources
        
    except Exception as e:
        print(f"Warning: Could not populate Sources column: {e}")
        # Add empty Sources column if OpenAlex join fails
        if 'Sources' not in df.columns:
            df['Sources'] = '-'
    
    # Add endpoint_citations column if not present
    if 'endpoint_citations' not in df.columns:
        df['endpoint_citations'] = '-'
        print("Added empty endpoint_citations column")
    
    # Skip numeric_measured_value column generation for now
    # The measured_value column already contains cleaned values
    print("Skipping numeric_measured_value column generation - using cleaned measured_value directly")
    
    print(f"Preprocessing completed. Final dataset has {len(df)} rows and {len(df.columns)} columns")
    return df

def apply_preprocessing(sheet_name: str, df: pd.DataFrame) -> pd.DataFrame:
    """
    Apply appropriate preprocessing based on sheet name.
    
    Args:
        sheet_name: Name of the Excel sheet or dataset type
        df: Raw DataFrame
        
    Returns:
        Preprocessed DataFrame
    """
    if sheet_name == 'Sheet1' or sheet_name == 'joined_dataset':
        return preprocess_combined_adc_endpoints_data(df)
    else:
        # Default: return unchanged for any unexpected sheet names
        print(f"No specific preprocessing defined for sheet '{sheet_name}', applying standard empty value cleanup")
        return standardize_empty_values(df)

def validate_data(df: pd.DataFrame, expected_columns: list) -> bool:
    """
    Validate that DataFrame has expected columns.
    
    Args:
        df: DataFrame to validate
        expected_columns: List of expected column names
        
    Returns:
        True if validation passes, False otherwise
    """
    missing_columns = set(expected_columns) - set(df.columns)
    if missing_columns:
        print(f"Warning: Missing columns: {missing_columns}")
        return False
    return True