#!/usr/bin/env python3
"""
Excel Data Analysis and Merging Script
Analyzes and merges data from AZ_ADC_ScaleUp_final_iter5_400_with_live_eval_ex_results_06_10_2025.xlsx
"""

import pandas as pd
import numpy as np
import os
from typing import <PERSON><PERSON>, Dict, Any

def load_excel_sheets(excel_path: str) -> Tuple[pd.DataFrame, pd.DataFrame]:
    """
    Load the two sheets from the Excel file.
    
    Args:
        excel_path: Path to the Excel file
        
    Returns:
        Tuple of (adcs_df, endpoints_df)
    """
    print(f"Loading Excel file: {excel_path}")
    
    # Check if file exists
    if not os.path.exists(excel_path):
        raise FileNotFoundError(f"Excel file not found: {excel_path}")
    
    # Get sheet names first
    excel_file = pd.ExcelFile(excel_path)
    sheet_names = excel_file.sheet_names
    print(f"Available sheets: {sheet_names}")
    
    # Load ADCs sheet
    adcs_df = pd.read_excel(excel_path, sheet_name='ADCs')
    print(f"ADCs sheet loaded: {len(adcs_df)} rows, {len(adcs_df.columns)} columns")
    print(f"ADCs columns: {list(adcs_df.columns)}")
    
    # Load Endpoints sheet - try different possible names
    endpoints_sheet_name = None
    possible_names = [
        'endpoints release 1 (filter for TP predicted class before merge)',
        'Endpoints_release1',
        'endpoints release 1',
        'Endpoints'
    ]
    
    for name in possible_names:
        if name in sheet_names:
            endpoints_sheet_name = name
            break
    
    if endpoints_sheet_name is None:
        print(f"Could not find endpoints sheet. Available sheets: {sheet_names}")
        # Let's try the second sheet if it exists
        if len(sheet_names) >= 2:
            endpoints_sheet_name = sheet_names[1]
            print(f"Using second sheet as endpoints: {endpoints_sheet_name}")
        else:
            raise ValueError("Could not identify endpoints sheet")
    
    endpoints_df = pd.read_excel(excel_path, sheet_name=endpoints_sheet_name)
    print(f"Endpoints sheet '{endpoints_sheet_name}' loaded: {len(endpoints_df)} rows, {len(endpoints_df.columns)} columns")
    print(f"Endpoints columns: {list(endpoints_df.columns)}")
    
    return adcs_df, endpoints_df

def identify_join_keys(adcs_df: pd.DataFrame, endpoints_df: pd.DataFrame) -> Tuple[list, list]:
    """
    Identify the join keys for merging the two dataframes.
    
    Args:
        adcs_df: ADCs dataframe
        endpoints_df: Endpoints dataframe
        
    Returns:
        Tuple of (common_columns, potential_join_keys)
    """
    print("\n=== IDENTIFYING JOIN KEYS ===")
    
    adcs_cols = set(adcs_df.columns)
    endpoints_cols = set(endpoints_df.columns)
    common_columns = list(adcs_cols.intersection(endpoints_cols))
    
    print(f"Common columns between sheets: {common_columns}")
    
    # Look for ID and ADC name columns
    potential_join_keys = []
    
    # Check for ID columns
    id_columns = [col for col in common_columns if 'id' in col.lower()]
    if id_columns:
        potential_join_keys.extend(id_columns)
        print(f"Found ID columns: {id_columns}")
    
    # Check for ADC name columns
    adc_name_columns = [col for col in common_columns if 'adc' in col.lower() and 'name' in col.lower()]
    if adc_name_columns:
        potential_join_keys.extend(adc_name_columns)
        print(f"Found ADC name columns: {adc_name_columns}")
    
    # Remove duplicates while preserving order
    potential_join_keys = list(dict.fromkeys(potential_join_keys))
    
    print(f"Potential join keys: {potential_join_keys}")
    
    return common_columns, potential_join_keys

def analyze_join_key_quality(adcs_df: pd.DataFrame, endpoints_df: pd.DataFrame, join_keys: list) -> Dict[str, Any]:
    """
    Analyze the quality of join keys.
    
    Args:
        adcs_df: ADCs dataframe
        endpoints_df: Endpoints dataframe
        join_keys: List of columns to use as join keys
        
    Returns:
        Dictionary with join key analysis
    """
    print(f"\n=== ANALYZING JOIN KEY QUALITY ===")
    
    analysis = {}
    
    for key in join_keys:
        print(f"\nAnalyzing join key: {key}")
        
        # Check data types
        adcs_dtype = adcs_df[key].dtype
        endpoints_dtype = endpoints_df[key].dtype
        print(f"  Data types - ADCs: {adcs_dtype}, Endpoints: {endpoints_dtype}")
        
        # Check for null values
        adcs_nulls = adcs_df[key].isnull().sum()
        endpoints_nulls = endpoints_df[key].isnull().sum()
        print(f"  Null values - ADCs: {adcs_nulls}, Endpoints: {endpoints_nulls}")
        
        # Check unique values
        adcs_unique = adcs_df[key].nunique()
        endpoints_unique = endpoints_df[key].nunique()
        print(f"  Unique values - ADCs: {adcs_unique}, Endpoints: {endpoints_unique}")
        
        # Check for overlapping values
        adcs_values = set(adcs_df[key].dropna().astype(str))
        endpoints_values = set(endpoints_df[key].dropna().astype(str))
        overlap = len(adcs_values.intersection(endpoints_values))
        print(f"  Overlapping values: {overlap}")
        
        analysis[key] = {
            'adcs_dtype': adcs_dtype,
            'endpoints_dtype': endpoints_dtype,
            'adcs_nulls': adcs_nulls,
            'endpoints_nulls': endpoints_nulls,
            'adcs_unique': adcs_unique,
            'endpoints_unique': endpoints_unique,
            'overlap': overlap,
            'adcs_total': len(adcs_df),
            'endpoints_total': len(endpoints_df)
        }
    
    return analysis

def preprocess_for_merge(adcs_df: pd.DataFrame, endpoints_df: pd.DataFrame, join_keys: list) -> Tuple[pd.DataFrame, pd.DataFrame]:
    """
    Preprocess dataframes for merging.
    
    Args:
        adcs_df: ADCs dataframe
        endpoints_df: Endpoints dataframe
        join_keys: List of join keys
        
    Returns:
        Tuple of preprocessed dataframes
    """
    print(f"\n=== PREPROCESSING FOR MERGE ===")
    
    adcs_processed = adcs_df.copy()
    endpoints_processed = endpoints_df.copy()
    
    # Convert join keys to string and uppercase for consistent matching
    for key in join_keys:
        if key in adcs_processed.columns:
            adcs_processed[key] = adcs_processed[key].astype(str).str.upper()
            print(f"Converted ADCs {key} to uppercase string")
        
        if key in endpoints_processed.columns:
            endpoints_processed[key] = endpoints_processed[key].astype(str).str.upper()
            print(f"Converted Endpoints {key} to uppercase string")
    
    # Filter endpoints to only TP predicted class if column exists
    if 'predicted_class' in endpoints_processed.columns:
        original_count = len(endpoints_processed)
        endpoints_processed = endpoints_processed[endpoints_processed['predicted_class'] == 'TP']
        filtered_count = len(endpoints_processed)
        print(f"Filtered endpoints to TP predicted class: {original_count} -> {filtered_count} rows")
    
    return adcs_processed, endpoints_processed

def perform_left_join(adcs_df: pd.DataFrame, endpoints_df: pd.DataFrame, join_keys: list) -> pd.DataFrame:
    """
    Perform left join between ADCs and Endpoints dataframes.
    
    Args:
        adcs_df: ADCs dataframe (left)
        endpoints_df: Endpoints dataframe (right)
        join_keys: List of columns to join on
        
    Returns:
        Merged dataframe
    """
    print(f"\n=== PERFORMING LEFT JOIN ===")
    print(f"Join keys: {join_keys}")
    print(f"ADCs shape before join: {adcs_df.shape}")
    print(f"Endpoints shape before join: {endpoints_df.shape}")
    
    # Perform the left join
    merged_df = pd.merge(
        adcs_df,
        endpoints_df,
        on=join_keys,
        how='left',
        suffixes=('_adcs', '_endpoints')
    )
    
    print(f"Merged shape after join: {merged_df.shape}")
    
    return merged_df

def analyze_merge_results(adcs_df: pd.DataFrame, endpoints_df: pd.DataFrame, merged_df: pd.DataFrame, join_keys: list) -> Dict[str, Any]:
    """
    Analyze the results of the merge operation.
    
    Args:
        adcs_df: Original ADCs dataframe
        endpoints_df: Original Endpoints dataframe
        merged_df: Merged dataframe
        join_keys: Join keys used
        
    Returns:
        Dictionary with merge analysis results
    """
    print(f"\n=== MERGE RESULTS ANALYSIS ===")
    
    analysis = {
        'original_adcs_count': len(adcs_df),
        'original_endpoints_count': len(endpoints_df),
        'merged_count': len(merged_df),
        'join_keys': join_keys
    }
    
    # Check if all ADCs are preserved
    adcs_preserved = len(merged_df) >= len(adcs_df)
    analysis['all_adcs_preserved'] = adcs_preserved
    print(f"All ADCs preserved: {adcs_preserved}")
    
    # Count rows with null endpoint values (ADCs without endpoints)
    endpoint_columns = [col for col in merged_df.columns if col in endpoints_df.columns and col not in join_keys]
    if endpoint_columns:
        # Check for rows where all endpoint columns are null
        endpoint_null_mask = merged_df[endpoint_columns].isnull().all(axis=1)
        null_endpoint_count = endpoint_null_mask.sum()
        analysis['rows_with_null_endpoints'] = null_endpoint_count
        print(f"Rows with null/missing endpoint values: {null_endpoint_count}")
    else:
        analysis['rows_with_null_endpoints'] = 0
        print("No endpoint-specific columns found to check for nulls")
    
    # Analyze duplicate join key combinations
    if join_keys:
        adcs_key_combos = adcs_df.groupby(join_keys).size()
        endpoints_key_combos = endpoints_df.groupby(join_keys).size()
        merged_key_combos = merged_df.groupby(join_keys).size()
        
        analysis['adcs_duplicate_keys'] = (adcs_key_combos > 1).sum()
        analysis['endpoints_duplicate_keys'] = (endpoints_key_combos > 1).sum()
        analysis['merged_duplicate_keys'] = (merged_key_combos > 1).sum()
        
        print(f"Duplicate key combinations - ADCs: {analysis['adcs_duplicate_keys']}, Endpoints: {analysis['endpoints_duplicate_keys']}, Merged: {analysis['merged_duplicate_keys']}")
    
    return analysis

def main():
    """Main analysis function."""
    # File path
    excel_path = "data/data/AZ_ADC_ScaleUp_final_iter5_400_with_live_eval_ex_results_06_10_2025.xlsx"
    
    try:
        # Step 1: Load the data
        adcs_df, endpoints_df = load_excel_sheets(excel_path)
        
        # Step 2: Identify join keys
        common_columns, potential_join_keys = identify_join_keys(adcs_df, endpoints_df)
        
        # Use the identified join keys or default to ['id', 'adc_name']
        if potential_join_keys:
            join_keys = potential_join_keys
        else:
            # Fallback to common column names
            join_keys = [col for col in ['id', 'adc_name'] if col in common_columns]
        
        if not join_keys:
            print("ERROR: No suitable join keys found!")
            return
        
        print(f"\nUsing join keys: {join_keys}")
        
        # Step 3: Analyze join key quality
        join_analysis = analyze_join_key_quality(adcs_df, endpoints_df, join_keys)
        
        # Step 4: Preprocess for merge
        adcs_processed, endpoints_processed = preprocess_for_merge(adcs_df, endpoints_df, join_keys)
        
        # Step 5: Perform left join
        merged_df = perform_left_join(adcs_processed, endpoints_processed, join_keys)
        
        # Step 6: Analyze merge results
        merge_analysis = analyze_merge_results(adcs_processed, endpoints_processed, merged_df, join_keys)
        
        # Step 7: Print final statistics
        print(f"\n=== FINAL STATISTICS ===")
        print(f"Original ADCs count: {merge_analysis['original_adcs_count']}")
        print(f"Original Endpoints count: {merge_analysis['original_endpoints_count']}")
        print(f"Merged dataframe count: {merge_analysis['merged_count']}")
        print(f"Rows with null/missing endpoint values: {merge_analysis['rows_with_null_endpoints']}")
        print(f"All ADCs preserved: {merge_analysis['all_adcs_preserved']}")
        
        # Compare with expected 8180 rows
        expected_rows = 8180
        print(f"\nComparison with expected {expected_rows} rows:")
        print(f"Difference: {merge_analysis['merged_count'] - expected_rows}")
        
        if merge_analysis['merged_count'] != expected_rows:
            print(f"DISCREPANCY DETECTED: Expected {expected_rows} rows, got {merge_analysis['merged_count']}")
            print("Possible reasons:")
            print(f"- Duplicate key combinations in ADCs: {merge_analysis.get('adcs_duplicate_keys', 0)}")
            print(f"- Duplicate key combinations in Endpoints: {merge_analysis.get('endpoints_duplicate_keys', 0)}")
            print("- Different preprocessing or filtering applied")
            print("- Different join keys used")
        
        # Save merged data for inspection
        output_path = "merged_analysis_results.xlsx"
        merged_df.to_excel(output_path, index=False)
        print(f"\nMerged data saved to: {output_path}")
        
        return merged_df, merge_analysis
        
    except Exception as e:
        print(f"Error during analysis: {e}")
        raise

if __name__ == "__main__":
    merged_df, analysis = main()
