#!/usr/bin/env python3
"""
Simplified ingestion script for ADC application database.
Reads single joined Excel file, applies preprocessing, and loads data into SQLite database.
"""

import sqlite3
import pandas as pd
import yaml
import argparse
import os
import sys
from typing import Dict, Any, List
from preprocessing import apply_preprocessing, validate_data

class SimplifiedADCDataIngester:
    def __init__(self, config_file: str):
        """Initialize the ingester with configuration."""
        self.config = self.load_config(config_file)
        self.db_connection = None
        
    def load_config(self, config_file: str) -> Dict[str, Any]:
        """Load configuration from YAML file."""
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            return config
        except Exception as e:
            print(f"Error loading config file: {e}")
            sys.exit(1)
    
    def connect_database(self, db_file: str) -> None:
        """Connect to SQLite database."""
        try:
            self.db_connection = sqlite3.connect(db_file)
            
            # Apply database settings
            db_settings = self.config.get('database_settings', {})
            if db_settings.get('foreign_keys', True):
                self.db_connection.execute("PRAGMA foreign_keys = ON")
            if db_settings.get('journal_mode'):
                self.db_connection.execute(f"PRAGMA journal_mode = {db_settings['journal_mode']}")
            if db_settings.get('synchronous'):
                self.db_connection.execute(f"PRAGMA synchronous = {db_settings['synchronous']}")
                
            print(f"Connected to database: {db_file}")
        except Exception as e:
            print(f"Error connecting to database: {e}")
            sys.exit(1)
    
    def create_table(self, table_name: str, df: pd.DataFrame, indexes: List[Dict]) -> None:
        """Create table with appropriate schema and indexes."""
        try:
            # Drop table if exists
            self.db_connection.execute(f"DROP TABLE IF EXISTS {table_name}")
            
            # Create table using pandas
            df.to_sql(table_name, self.db_connection, index=False, if_exists='replace')
            
            # Create indexes
            for index_config in indexes:
                column = index_config['column']
                index_type = index_config['type']
                index_name = f"idx_{table_name}_{column}"
                
                if index_type == 'primary':
                    # Note: SQLite auto-creates rowid, we'll use regular index for non-unique primary keys
                    self.db_connection.execute(f"CREATE INDEX IF NOT EXISTS {index_name} ON {table_name}({column})")
                elif index_type == 'index':
                    self.db_connection.execute(f"CREATE INDEX IF NOT EXISTS {index_name} ON {table_name}({column})")
                elif index_type == 'unique':
                    self.db_connection.execute(f"CREATE UNIQUE INDEX IF NOT EXISTS {index_name} ON {table_name}({column})")
            
            self.db_connection.commit()
            print(f"Created table '{table_name}' with {len(df)} rows and {len(indexes)} indexes")
            
        except Exception as e:
            print(f"Error creating table {table_name}: {e}")
            raise
    
    def process_sheets(self, excel_file: str) -> None:
        """Process and join the two Excel sheets."""
        try:
            print(f"Processing sheets from: {excel_file}")
            
            # Get sheet configurations
            sheets_config = self.config.get('sheets', {})
            
            # Read both sheets
            adcs_df = None
            endpoints_df = None
            
            for sheet_name, sheet_config in sheets_config.items():
                print(f"Reading sheet: {sheet_name}")
                df = pd.read_excel(excel_file, sheet_name=sheet_name)
                print(f"Read {len(df)} rows from sheet '{sheet_name}'")
                
                if sheet_name == 'ADCs':
                    adcs_df = df
                elif sheet_name == 'Endpoints_release1':
                    endpoints_df = df
            
            if adcs_df is None or endpoints_df is None:
                raise ValueError("Both ADCs and Endpoints_release1 sheets are required")
            
            # Perform join on id and adc_name
            print("Joining ADCs and Endpoints_release1 sheets...")
            print(f"ADCs sheet: {len(adcs_df)} rows")
            print(f"Endpoints sheet: {len(endpoints_df)} rows")
            
        
            endpoints_df['id'] = endpoints_df['id'].astype(str).str.upper()
            endpoints_df = endpoints_df[endpoints_df['predicted_class'] == 'TP']            
            adcs_df['id'] = adcs_df['id'].astype(str).str.upper()
            # Join the datasets
            joined_df = pd.merge(
                adcs_df,
                endpoints_df,
                on=['id', 'adc_name'], 
                how='left',
                suffixes=('_endpoints', '_adcs')
            )
            
            print(f"Joined dataset: {len(joined_df)} rows")
            
            # Handle duplicate columns from the join
            # Remove duplicate columns, keeping the endpoints version for most cases
            cols_to_drop = []
            for col in joined_df.columns:
                if col.endswith('_adcs') and col.replace('_adcs', '_endpoints') in joined_df.columns:
                    # Keep endpoints version, drop ADCs version for duplicates
                    if col.replace('_adcs', '') not in ['id', 'adc_name']:  # Keep join keys from both
                        cols_to_drop.append(col)
                elif col.endswith('_endpoints'):
                    # Rename endpoints columns back to original names
                    base_col = col.replace('_endpoints', '')
                    if base_col not in joined_df.columns:
                        joined_df[base_col] = joined_df[col]
                        cols_to_drop.append(col)
            
            # Drop duplicate columns
            if cols_to_drop:
                joined_df = joined_df.drop(columns=cols_to_drop)
                print(f"Removed {len(cols_to_drop)} duplicate columns after join")
            
            # Validate expected columns
            expected_columns = sheets_config['Endpoints_release1'].get('expected_columns', [])
            if expected_columns and not validate_data(joined_df, expected_columns):
                print(f"Warning: Column validation failed for joined dataset")
            
            # Apply preprocessing to joined dataset
            joined_df = apply_preprocessing('joined_dataset', joined_df)
            
            # Filter to only expected columns if specified
            if expected_columns:
                available_columns = [col for col in expected_columns if col in joined_df.columns]
                joined_df = joined_df[available_columns]
                print(f"Filtered to {len(available_columns)} expected columns")
            
            # Create table and indexes
            table_name = sheets_config['Endpoints_release1'].get('table_name', 'adc_endpoints')
            indexes = sheets_config['Endpoints_release1'].get('indexes', [])
            
            self.create_table(table_name, joined_df, indexes)
            
        except Exception as e:
            print(f"Error processing sheets: {e}")
            raise
    
    def ingest_data(self, excel_file: str) -> None:
        """Main ingestion process."""
        try:
            print(f"Starting data ingestion from: {excel_file}")
            
            # Verify Excel file exists
            if not os.path.exists(excel_file):
                print(f"Error: Excel file not found: {excel_file}")
                sys.exit(1)
            
            # Process and join the two sheets
            self.process_sheets(excel_file)
            
            print("Data ingestion completed successfully!")
            
        except Exception as e:
            print(f"Error during ingestion: {e}")
            if self.db_connection:
                self.db_connection.rollback()
            raise
        finally:
            if self.db_connection:
                self.db_connection.close()
    
    def get_database_stats(self, db_file: str) -> None:
        """Print database statistics after ingestion."""
        try:
            conn = sqlite3.connect(db_file)
            cursor = conn.cursor()
            
            print("\nDatabase Statistics:")
            print("=" * 50)
            
            # Get all tables
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = cursor.fetchall()
            
            for (table_name,) in tables:
                cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                count = cursor.fetchone()[0]
                print(f"Table '{table_name}': {count} rows")
            
            # Get indexes
            cursor.execute("SELECT name, tbl_name FROM sqlite_master WHERE type='index'")
            indexes = cursor.fetchall()
            print(f"\nTotal indexes: {len(indexes)}")
            
            conn.close()
            
        except Exception as e:
            print(f"Error getting database stats: {e}")

def main():
    parser = argparse.ArgumentParser(description='Ingest simplified ADC data into SQLite database')
    parser.add_argument('excel_file', help='Path to joined Excel file')
    parser.add_argument('db_file', help='Path to SQLite database file')
    parser.add_argument('config_file', help='Path to index plan YAML file')
    parser.add_argument('--stats', action='store_true', help='Show database statistics after ingestion')
    
    args = parser.parse_args()
    
    # Initialize ingester
    ingester = SimplifiedADCDataIngester(args.config_file)
    
    # Connect to database
    ingester.connect_database(args.db_file)
    
    # Ingest data
    ingester.ingest_data(args.excel_file)
    
    # Show stats if requested
    if args.stats:
        ingester.get_database_stats(args.db_file)

if __name__ == "__main__":
    main()