# Index Plan Configuration for Simplified ADC Application Database
# This file defines the two sheets to join from the new Excel file structure

source_file: "data/AZ_ADC_ScaleUp_final_iter5_400_with_live_eval_ex_results (2).xlsx"
database_file: "data/adc_database.db"

# Two sheets to process and join into combined ADC and Endpoint data
sheets:
  ADCs:
    table_name: "adc_endpoints"
    description: "ADC information sheet to be joined with endpoints"
    join_with: "Endpoints_release1"
    join_keys: ["id", "adc_name"]
  Endpoints_release1:
    table_name: "adc_endpoints"
    description: "Endpoints information sheet to be joined with ADCs"
    join_with: "ADCs"
    join_keys: ["id", "adc_name"]
    expected_columns:
      # Combined columns from both sheets after join
      # ADC columns from ADCs sheet
      - id
      - adc_name
      - adc_company
      - antibody_name
      - antibody_clonality
      - antibody_species
      - antibody_isotype
      - antibody_binding_epitope
      - antibody_binding_epitope_location
      - adc_species_cross_reactivity
      - payload_name
      - payload_target
      - linker_name
      - linker_type
      - antigen_name
      - antigen_name_standardized
      - ss_conjugation
      - ss_conjugation_technology
      - conjugation_amino_acid
      - conjugation_sites
      - drug_to_antibody_ratio
      - clinical_trial_phase
      - clinical_data_availability
      - adc_citations
      # Endpoint columns from Endpoints_release1 sheet
      - extraction_id
      - model_name
      - model_type
      - experiment_type
      - cancer_type
      - cancer_type_standardized
      - cancer_subtype
      - endpoint_name
      - measured_value
      - measured_dose
      - measured_concentration
      - measured_timepoint
      - measured_death_percentage
      - endpoint_citations
      - endpoint_category
      - Sources
    indexes:
      # Key ADC indexes
      - column: id
        type: "index"
      - column: adc_name
        type: "index"
      - column: adc_company
        type: "index"
      - column: antigen_name
        type: "index"
      - column: antigen_name_standardized
        type: "index"
      - column: clinical_trial_phase
        type: "index"
      # Key Endpoint indexes
      - column: extraction_id
        type: "index"
      - column: model_name
        type: "index"
      - column: endpoint_name
        type: "index"
      - column: experiment_type
        type: "index"
      - column: endpoint_category
        type: "index"
      - column: cancer_type
        type: "index"
      - column: cancer_type_standardized
        type: "index"
      - column: cancer_subtype
        type: "index"

# Global database settings
database_settings:
  foreign_keys: true
  journal_mode: "WAL"
  synchronous: "NORMAL"